package repository

import (
	"github.com/solarchapter-tech/water-iq-backend/config.go"
	"github.com/solarchapter-tech/water-iq-backend/model"
)

type DeviceVersionRepository interface {
	Insert(deviceVersion *model.DeviceVersion) (*model.DeviceVersion, error)
	GetLatestByDeviceID(deviceID int64) (*model.DeviceVersion, error)
	GetAllByDeviceID(deviceID int64) ([]model.DeviceVersion, error)
}

type DeviceVersionRepositoryCtx struct{}

func (c *DeviceVersionRepositoryCtx) Insert(deviceVersion *model.DeviceVersion) (*model.DeviceVersion, error) {
	db := config.DbManager()
	err := db.Create(deviceVersion).Error
	if err != nil {
		return nil, err
	}

	return deviceVersion, nil
}

func (c *DeviceVersionRepositoryCtx) GetLatestByDeviceID(deviceID int64) (*model.DeviceVersion, error) {
	db := config.DbManager()
	var deviceVersion model.DeviceVersion
	err := db.Where("device_id = ?", deviceID).Order("created_at DESC").First(&deviceVersion).Error
	if err != nil {
		return nil, err
	}
	return &deviceVersion, nil
}

func (c *DeviceVersionRepositoryCtx) GetAllByDeviceID(deviceID int64) ([]model.DeviceVersion, error) {
	db := config.DbManager()
	var deviceVersions []model.DeviceVersion
	err := db.Where("device_id = ?", deviceID).Order("created_at DESC").Find(&deviceVersions).Error
	if err != nil {
		return nil, err
	}
	return deviceVersions, nil
}
