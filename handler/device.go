package handler

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/labstack/echo"
	"github.com/solarchapter-tech/water-iq-backend/model"
	"github.com/solarchapter-tech/water-iq-backend/service"
	"github.com/solarchapter-tech/water-iq-backend/utils"
)

type DeviceHandler struct {
	DeviceService service.DeviceService
}

func (deviceHandler *DeviceHandler) DeviceInstall(c echo.Context) error {
	params := new(model.InstallDeviceRequest)

	err := c.Bind(params)
	if err != nil {
		return c.JSON(http.StatusBadRequest, model.BasicResp{Message: err.Error()})
	}

	err = validateInstallDevice(params)
	if err != nil {
		return c.JSON(http.StatusBadRequest, model.BasicResp{Message: err.Error()})
	}

	err = deviceHandler.DeviceService.InstallDevice(params)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, model.BasicResp{Message: err.Error()})
	}

	resp := model.BasicResp{
		Message: utils.Success,
	}

	return c.JSON(http.StatusOK, resp)
}

func (deviceHandler *DeviceHandler) LogStatus(c echo.Context) error {
	reqs := new([]model.LogStatusRequest)

	err := c.Bind(reqs)
	if err != nil {
		errMsg := fmt.Errorf("req: %s || error: %s", utils.PrettyStruct(&reqs), err.Error())
		utils.SendServerError(errMsg, "DeviceHandler.LogStatus.Bind")
		return c.JSON(http.StatusBadRequest, model.BasicResp{Message: err.Error()})
	}

	params := make([]model.LogStatusRequest, len(*reqs))
	copy(params, *reqs)

	params, err = validateLogStatus(params)
	if err != nil {
		errMsg := fmt.Errorf("req: %s || error: %s", utils.PrettyStruct(params), err.Error())
		utils.SendServerError(errMsg, "DeviceHandler.LogStatus.validateLogStatus")
		return c.JSON(http.StatusBadRequest, model.BasicResp{Message: err.Error()})
	}

	err = deviceHandler.DeviceService.LogStatus(params)
	if err != nil {
		errMsg := fmt.Errorf("req: %s || error: %s", utils.PrettyStruct(params), err.Error())
		utils.SendServerError(errMsg, "DeviceHandler.LogStatus.DeviceService.LogStatus")
		return c.JSON(http.StatusInternalServerError, model.BasicResp{Message: err.Error()})
	}

	resp := model.BasicResp{
		Message: utils.Success,
	}

	return c.JSON(http.StatusOK, resp)
}

func (deviceHandler *DeviceHandler) LogStream(c echo.Context) error {
	reqs := new([]model.LogStreamRequest)

	err := c.Bind(reqs)
	if err != nil {
		errMsg := fmt.Errorf("req: %s || error: %s", utils.PrettyStruct(&reqs), err.Error())
		utils.SendServerError(errMsg, "DeviceHandler.LogStream.Bind")
		return c.JSON(http.StatusBadRequest, model.BasicResp{Message: err.Error()})
	}

	params := make([]model.LogStreamRequest, len(*reqs))
	copy(params, *reqs)

	params, err = validateLogStream(params)
	if err != nil {
		errMsg := fmt.Errorf("req: %s || error: %s", utils.PrettyStruct(params), err.Error())
		utils.SendServerError(errMsg, "DeviceHandler.LogStream.validateLogStream")
		return c.JSON(http.StatusBadRequest, model.BasicResp{Message: err.Error()})
	}

	err = deviceHandler.DeviceService.LogStream(params)
	if err != nil {
		errMsg := fmt.Errorf("req: %s || error: %s", utils.PrettyStruct(params), err.Error())
		utils.SendServerError(errMsg, "DeviceHandler.LogStream.DeviceService.LogStream")
		return c.JSON(http.StatusInternalServerError, model.BasicResp{Message: err.Error()})
	}

	resp := model.BasicResp{
		Message: utils.Success,
	}

	return c.JSON(http.StatusOK, resp)
}

func (deviceHandler *DeviceHandler) LogFile(c echo.Context) error {
	// Parse multipart form manually
	deviceCode := c.FormValue("device_code")
	timestamp := c.FormValue("timestamp")
	file, err := c.FormFile("file")
	if err != nil {
		errMsg := fmt.Errorf("error getting file: %s", err.Error())
		utils.SendServerError(errMsg, "DeviceHandler.LogFile.FormFile")
		return c.JSON(http.StatusBadRequest, model.BasicResp{Message: "file cannot be empty"})
	}

	params := &model.LogFileRequest{
		DeviceCode: deviceCode,
		Timestamp:  timestamp,
		File:       file,
	}

	err = validateLogFile(params)
	if err != nil {
		errMsg := fmt.Errorf("req: %s || error: %s", utils.PrettyStruct(params), err.Error())
		utils.SendServerError(errMsg, "DeviceHandler.LogFile.validateLogFile")
		return c.JSON(http.StatusBadRequest, model.BasicResp{Message: err.Error()})
	}

	err = deviceHandler.DeviceService.LogFile(params)
	if err != nil {
		errMsg := fmt.Errorf("req: %s || error: %s", utils.PrettyStruct(params), err.Error())
		utils.SendServerError(errMsg, "DeviceHandler.LogFile.DeviceService.LogFile")
		return c.JSON(http.StatusInternalServerError, model.BasicResp{Message: err.Error()})
	}

	resp := model.BasicResp{
		Message: utils.Success,
	}

	return c.JSON(http.StatusOK, resp)
}

func (deviceHandler *DeviceHandler) DeviceList(c echo.Context) error {
	params := new(model.DeviceListRequest)
	villageIDs := c.Get("villageIDs").([]int64)

	err := c.Bind(params)
	if err != nil {
		return c.JSON(http.StatusBadRequest, model.BasicResp{Message: err.Error()})
	}
	params.VillageIDs = villageIDs

	err = validateDeviceList(params)
	if err != nil {
		return c.JSON(http.StatusBadRequest, model.BasicResp{Message: err.Error()})
	}

	devices, err := deviceHandler.DeviceService.DeviceList(params)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, model.BasicResp{Message: err.Error()})
	}

	resp := model.BasicResp{
		Message: utils.Success,
		Data:    devices,
	}

	return c.JSON(http.StatusOK, resp)
}

func (deviceHandler *DeviceHandler) StatusLogs(c echo.Context) error {
	params := new(model.StatusLogsRequest)

	err := c.Bind(params)
	if err != nil {
		return c.JSON(http.StatusBadRequest, model.BasicResp{Message: err.Error()})
	}

	// TODO do inside validate
	if params.PageSize == 0 {
		params.PageSize = 20
	}
	if params.PageNumber == 0 {
		params.PageNumber = 1
	}
	if params.SortBy == "" {
		params.SortBy = "created_at"
	}
	if params.OrderBy == "" {
		params.OrderBy = utils.OrderByDesc
	}

	status, err := deviceHandler.DeviceService.StatusLogs(params)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, model.BasicResp{Message: err.Error()})
	}

	resp := model.BasicResp{
		Message: utils.Success,
		Data:    status.Statuses,
		Meta:    status.Meta,
	}

	return c.JSON(http.StatusOK, resp)
}

func (deviceHandler *DeviceHandler) FunctionalityStats(c echo.Context) error {
	params := new(model.DeviceFunctionalityStatsRequest)
	villageIDs := c.Get("villageIDs").([]int64)
	roles := c.Get("roles").([]string)

	err := c.Bind(params)
	if err != nil {
		return c.JSON(http.StatusBadRequest, model.BasicResp{Message: err.Error()})
	}
	params.VillageIDs = villageIDs
	params.Roles = roles

	err = validateDeviceFunctionalityStats(params)
	if err != nil {
		return c.JSON(http.StatusBadRequest, model.BasicResp{Message: err.Error()})
	}

	functionality, err := deviceHandler.DeviceService.FunctionalityStats(params)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, model.BasicResp{Message: err.Error()})
	}

	resp := model.BasicResp{
		Message: utils.Success,
		Data:    functionality,
	}

	return c.JSON(http.StatusOK, resp)
}

func (deviceHandler *DeviceHandler) Save(c echo.Context) error {
	params := new(model.SaveDeviceRequest)

	err := c.Bind(params)
	if err != nil {
		return c.JSON(http.StatusBadRequest, model.BasicResp{Message: err.Error()})
	}

	err = deviceHandler.DeviceService.Save(params)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, model.BasicResp{Message: err.Error()})
	}

	resp := model.BasicResp{
		Message: utils.Success,
	}

	return c.JSON(http.StatusOK, resp)
}

func (deviceHandler *DeviceHandler) Calibrate(c echo.Context) error {
	params := new(model.CalibrateDeviceRequest)

	err := c.Bind(params)
	if err != nil {
		return c.JSON(http.StatusBadRequest, model.BasicResp{Message: err.Error()})
	}

	err = validateCalibrateDevice(params)
	if err != nil {
		return c.JSON(http.StatusBadRequest, model.BasicResp{Message: err.Error()})
	}

	err = deviceHandler.DeviceService.Calibrate(params)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, model.BasicResp{Message: err.Error()})
	}

	resp := model.BasicResp{
		Message: utils.Success,
	}

	return c.JSON(http.StatusOK, resp)
}

func (deviceHandler *DeviceHandler) DeviceCalibrationList(c echo.Context) error {
	params := new(model.DeviceCalibrationListRequest)
	villageIDs := c.Get("villageIDs").([]int64)

	err := c.Bind(params)
	if err != nil {
		return c.JSON(http.StatusBadRequest, model.BasicResp{Message: err.Error()})
	}
	params.VillageIDs = villageIDs

	err = validateDeviceCalibrationList(params)
	if err != nil {
		return c.JSON(http.StatusBadRequest, model.BasicResp{Message: err.Error()})
	}

	devices, err := deviceHandler.DeviceService.DeviceCalibrationList(params)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, model.BasicResp{Message: err.Error()})
	}

	resp := model.BasicResp{
		Message: utils.Success,
		Data:    devices,
	}

	return c.JSON(http.StatusOK, resp)
}

func (deviceHandler *DeviceHandler) Saved(c echo.Context) error {
	params := new(model.SavedDeviceRequest)

	err := c.Bind(params)
	if err != nil {
		return c.JSON(http.StatusBadRequest, model.BasicResp{Message: err.Error()})
	}

	err = validateSevedDevice(params)
	if err != nil {
		return c.JSON(http.StatusBadRequest, model.BasicResp{Message: err.Error()})
	}

	device, err := deviceHandler.DeviceService.Saved(params)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, model.BasicResp{Message: err.Error()})
	}

	resp := model.BasicResp{
		Message: utils.Success,
		Data:    device,
	}

	return c.JSON(http.StatusOK, resp)
}

func (deviceHandler *DeviceHandler) Calibrated(c echo.Context) error {
	params := new(model.CalibratedDeviceRequest)

	err := c.Bind(params)
	if err != nil {
		return c.JSON(http.StatusBadRequest, model.BasicResp{Message: err.Error()})
	}

	err = validateCalibratedDevice(params)
	if err != nil {
		return c.JSON(http.StatusBadRequest, model.BasicResp{Message: err.Error()})
	}

	calibrated, err := deviceHandler.DeviceService.Calibrated(params)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, model.BasicResp{Message: err.Error()})
	}

	resp := model.BasicResp{
		Message: utils.Success,
		Data:    calibrated,
	}

	return c.JSON(http.StatusOK, resp)
}

func (deviceHandler *DeviceHandler) UploadVersion(c echo.Context) error {
	// Parse multipart form manually
	deviceIDStr := c.FormValue("device_id")
	deviceID, err := strconv.ParseInt(deviceIDStr, 10, 64)
	if err != nil {
		errMsg := fmt.Errorf("error parsing device id: %s", err.Error())
		utils.SendServerError(errMsg, "DeviceHandler.UploadVersion.ParseInt")
		return c.JSON(http.StatusBadRequest, model.BasicResp{Message: "invalid device id"})
	}

	version := c.FormValue("v")
	file, err := c.FormFile("file")
	if err != nil {
		errMsg := fmt.Errorf("error getting file: %s", err.Error())
		utils.SendServerError(errMsg, "DeviceHandler.UploadVersion.FormFile")
		return c.JSON(http.StatusBadRequest, model.BasicResp{Message: "file cannot be empty"})
	}

	params := &model.UploadVersionRequest{
		Version:  version,
		File:     file,
		DeviceID: deviceID,
	}

	err = validateUploadVersion(params)
	if err != nil {
		errMsg := fmt.Errorf("req: %s || error: %s", utils.PrettyStruct(params), err.Error())
		utils.SendServerError(errMsg, "DeviceHandler.UploadVersion.validateUploadVersion")
		return c.JSON(http.StatusBadRequest, model.BasicResp{Message: err.Error()})
	}

	err = deviceHandler.DeviceService.UploadVersion(params)
	if err != nil {
		errMsg := fmt.Errorf("req: %s || error: %s", utils.PrettyStruct(params), err.Error())
		utils.SendServerError(errMsg, "DeviceHandler.UploadVersion.DeviceService.UploadVersion")
		return c.JSON(http.StatusInternalServerError, model.BasicResp{Message: err.Error()})
	}

	resp := model.BasicResp{
		Message: utils.Success,
	}

	return c.JSON(http.StatusOK, resp)
}

func (deviceHandler *DeviceHandler) CheckVersion(c echo.Context) error {
	params := new(model.CheckVersionRequest)

	err := c.Bind(params)
	if err != nil {
		return c.JSON(http.StatusBadRequest, model.BasicResp{Message: err.Error()})
	}

	err = validateCheckVersion(params)
	if err != nil {
		errMsg := fmt.Errorf("req: %s || error: %s", utils.PrettyStruct(params), err.Error())
		utils.SendServerError(errMsg, "DeviceHandler.CheckVersion.validateCheckVersion")
		return c.JSON(http.StatusBadRequest, model.BasicResp{Message: err.Error()})
	}

	versionCheck, err := deviceHandler.DeviceService.CheckVersion(params)
	if err != nil {
		errMsg := fmt.Errorf("req: %s || error: %s", utils.PrettyStruct(params), err.Error())
		utils.SendServerError(errMsg, "DeviceHandler.CheckVersion.DeviceService.CheckVersion")
		return c.JSON(http.StatusInternalServerError, model.BasicResp{Message: err.Error()})
	}

	resp := model.BasicResp{
		Message: utils.Success,
		Data:    versionCheck,
	}

	return c.JSON(http.StatusOK, resp)
}

func (deviceHandler *DeviceHandler) DeviceVersionList(c echo.Context) error {
	params := new(model.DeviceVersionListRequest)

	err := c.Bind(params)
	if err != nil {
		return c.JSON(http.StatusBadRequest, model.BasicResp{Message: err.Error()})
	}

	err = validateDeviceVersionList(params)
	if err != nil {
		errMsg := fmt.Errorf("req: %s || error: %s", utils.PrettyStruct(params), err.Error())
		utils.SendServerError(errMsg, "DeviceHandler.DeviceVersionList.validateDeviceVersionList")
		return c.JSON(http.StatusBadRequest, model.BasicResp{Message: err.Error()})
	}

	versionList, err := deviceHandler.DeviceService.DeviceVersionList(params)
	if err != nil {
		errMsg := fmt.Errorf("req: %s || error: %s", utils.PrettyStruct(params), err.Error())
		utils.SendServerError(errMsg, "DeviceHandler.DeviceVersionList.DeviceService.DeviceVersionList")
		return c.JSON(http.StatusInternalServerError, model.BasicResp{Message: err.Error()})
	}

	resp := model.BasicResp{
		Message: utils.Success,
		Data:    versionList,
	}

	return c.JSON(http.StatusOK, resp)
}
