package model

import (
	"mime/multipart"
	"time"
)

type DeviceVersion struct {
	ID        int64     `gorm:"column:id;primary_key"`
	DeviceID  int64     `gorm:"column:device_id"`
	Value     string    `gorm:"column:value"`
	URL       string    `gorm:"column:url"`
	CreatedAt time.Time `gorm:"column:created_at"`

	Device Device `gorm:"foreignkey:DeviceID"`
}

type UploadVersionRequest struct {
	Version  string                `form:"v"`
	File     *multipart.FileHeader `form:"file"`
	DeviceID int64                 `form:"device_id"`
}

type CheckVersionRequest struct {
	Version    string `query:"v"`
	DeviceCode string `query:"device_code"`
	DeviceID   int64  // Internal field set during validation
}

type CheckVersionResponse struct {
	IsLatest bool   `json:"is_latest"`
	FileURL  string `json:"file_url"`
}

type DeviceVersionListRequest struct {
	DeviceID int64 `query:"device_id"`
}

type DeviceVersionListResponse struct {
	DeviceID int64                   `json:"device_id"`
	Version  []DeviceVersionListItem `json:"version"`
}

type DeviceVersionListItem struct {
	ID        int64     `json:"id"`
	Value     string    `json:"value"`
	FileURL   string    `json:"file_url"`
	CreatedAt time.Time `json:"created_at"`
}
